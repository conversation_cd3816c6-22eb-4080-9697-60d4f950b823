#!/usr/bin/env python3
"""
Time Series Analysis and Connectivity Analysis for EEG-MRI Integration
Analyzes temporal dynamics and connectivity patterns between lesioned and healthy regions
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import gzip
import warnings
warnings.filterwarnings('ignore')

# Neuroimaging and EEG libraries
import nibabel as nib
from nilearn import plotting, image
import mne

# Scientific computing
from scipy import stats, signal
from scipy.spatial.distance import pdist, squareform
from sklearn.decomposition import PCA, FastICA
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mutual_info_score

# Set up plotting
plt.style.use('default')
sns.set_palette("husl")

class TimeSeriesConnectivityAnalyzer:
    """
    Comprehensive time series and connectivity analysis for EEG-MRI integration
    """
    
    def __init__(self, eeg_path="1252141", mri_path="masks-2"):
        self.eeg_path = Path(eeg_path)
        self.mri_path = Path(mri_path)
        
        # EEG configuration
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                            'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        self.sampling_rate = 128  # Hz
        
        # Frequency bands
        self.freq_bands = {
            'delta': (1, 4),
            'theta': (4, 8), 
            'alpha': (8, 12),
            'beta': (12, 30),
            'gamma': (30, 50)
        }
        
        # Initialize storage
        self.time_series_results = {}
        self.connectivity_results = {}
        self.lesion_connectivity = {}
        
        print("Time Series Connectivity Analyzer Initialized")
    
    def load_eeg_data(self, subject_id, dataset='guinea_bissau'):
        """Load EEG data for a subject"""
        if dataset == 'guinea_bissau':
            filepath = self.eeg_path / "EEGs_Guinea-Bissau" / f"signal-{subject_id}.csv.gz"
        else:  # nigeria
            matching_files = list((self.eeg_path / "EEGs_Nigeria").glob(f"signal-{subject_id}-*.csv.gz"))
            if not matching_files:
                raise FileNotFoundError(f"No EEG file found for subject {subject_id}")
            filepath = matching_files[0]
        
        with gzip.open(filepath, 'rt') as f:
            data = pd.read_csv(f)
        
        eeg_data = data[self.eeg_channels].values.T
        return eeg_data, data
    
    def load_lesion_mask(self, subject_id):
        """Load lesion mask for a subject"""
        mask_path = self.mri_path / str(subject_id) / f"{subject_id}_MaskInOrig.nii.gz"
        
        if not mask_path.exists():
            raise FileNotFoundError(f"Lesion mask not found: {mask_path}")
        
        lesion_img = nib.load(mask_path)
        lesion_data = lesion_img.get_fdata()
        
        return lesion_data, lesion_img
    
    def extract_frequency_band_activity(self, eeg_data, band_name):
        """Extract activity in specific frequency band"""
        if band_name not in self.freq_bands:
            raise ValueError(f"Unknown frequency band: {band_name}")
        
        low_freq, high_freq = self.freq_bands[band_name]
        
        # Apply bandpass filter to each channel
        filtered_data = np.zeros_like(eeg_data)
        
        for i in range(eeg_data.shape[0]):
            # Design Butterworth bandpass filter
            sos = signal.butter(4, [low_freq, high_freq], btype='band', 
                              fs=self.sampling_rate, output='sos')
            filtered_data[i, :] = signal.sosfilt(sos, eeg_data[i, :])
        
        return filtered_data
    
    def compute_instantaneous_power(self, eeg_data):
        """Compute instantaneous power using Hilbert transform"""
        analytic_signal = signal.hilbert(eeg_data, axis=1)
        instantaneous_power = np.abs(analytic_signal)**2
        return instantaneous_power
    
    def compute_phase_coupling(self, eeg_data):
        """Compute phase coupling between channels"""
        n_channels = eeg_data.shape[0]
        
        # Compute analytic signal for each channel
        analytic_signals = signal.hilbert(eeg_data, axis=1)
        phases = np.angle(analytic_signals)
        
        # Compute phase coupling matrix
        phase_coupling = np.zeros((n_channels, n_channels))
        
        for i in range(n_channels):
            for j in range(i+1, n_channels):
                # Phase difference
                phase_diff = phases[i, :] - phases[j, :]
                
                # Phase locking value (PLV)
                plv = np.abs(np.mean(np.exp(1j * phase_diff)))
                phase_coupling[i, j] = plv
                phase_coupling[j, i] = plv
        
        return phase_coupling
    
    def compute_coherence_matrix(self, eeg_data, window_length=256):
        """Compute coherence matrix between all channel pairs"""
        n_channels = eeg_data.shape[0]
        coherence_matrix = np.zeros((n_channels, n_channels))
        
        for i in range(n_channels):
            for j in range(i, n_channels):
                # Compute coherence using Welch's method
                f, Cxy = signal.coherence(eeg_data[i, :], eeg_data[j, :], 
                                        fs=self.sampling_rate, nperseg=window_length)
                
                # Average coherence across frequencies
                mean_coherence = np.mean(Cxy)
                coherence_matrix[i, j] = mean_coherence
                coherence_matrix[j, i] = mean_coherence
        
        return coherence_matrix
    
    def compute_mutual_information_matrix(self, eeg_data, n_bins=50):
        """Compute mutual information matrix between channels"""
        n_channels = eeg_data.shape[0]
        mi_matrix = np.zeros((n_channels, n_channels))
        
        for i in range(n_channels):
            for j in range(i, n_channels):
                # Discretize signals for mutual information calculation
                signal_i = np.digitize(eeg_data[i, :], 
                                     bins=np.linspace(eeg_data[i, :].min(), 
                                                    eeg_data[i, :].max(), n_bins))
                signal_j = np.digitize(eeg_data[j, :], 
                                     bins=np.linspace(eeg_data[j, :].min(), 
                                                    eeg_data[j, :].max(), n_bins))
                
                # Compute mutual information
                mi = mutual_info_score(signal_i, signal_j)
                mi_matrix[i, j] = mi
                mi_matrix[j, i] = mi
        
        return mi_matrix
    
    def analyze_temporal_dynamics(self, eeg_data, window_size=1280, overlap=0.5):
        """Analyze temporal dynamics using sliding window approach"""
        n_channels, n_samples = eeg_data.shape
        step_size = int(window_size * (1 - overlap))
        
        # Calculate number of windows
        n_windows = (n_samples - window_size) // step_size + 1
        
        # Initialize storage for temporal features
        temporal_features = {
            'time_points': [],
            'power_evolution': {band: [] for band in self.freq_bands.keys()},
            'connectivity_evolution': [],
            'phase_coupling_evolution': []
        }
        
        for w in range(n_windows):
            start_idx = w * step_size
            end_idx = start_idx + window_size
            
            if end_idx > n_samples:
                break
            
            window_data = eeg_data[:, start_idx:end_idx]
            time_point = (start_idx + end_idx) / 2 / self.sampling_rate
            
            temporal_features['time_points'].append(time_point)
            
            # Compute power in each frequency band
            for band_name in self.freq_bands.keys():
                band_data = self.extract_frequency_band_activity(window_data, band_name)
                band_power = self.compute_instantaneous_power(band_data)
                mean_power = np.mean(band_power, axis=1)  # Average across time
                temporal_features['power_evolution'][band_name].append(mean_power)
            
            # Compute connectivity measures
            coherence_matrix = self.compute_coherence_matrix(window_data)
            temporal_features['connectivity_evolution'].append(coherence_matrix)
            
            phase_coupling = self.compute_phase_coupling(window_data)
            temporal_features['phase_coupling_evolution'].append(phase_coupling)
        
        # Convert lists to arrays
        temporal_features['time_points'] = np.array(temporal_features['time_points'])
        
        for band_name in self.freq_bands.keys():
            temporal_features['power_evolution'][band_name] = np.array(
                temporal_features['power_evolution'][band_name])
        
        temporal_features['connectivity_evolution'] = np.array(
            temporal_features['connectivity_evolution'])
        temporal_features['phase_coupling_evolution'] = np.array(
            temporal_features['phase_coupling_evolution'])
        
        return temporal_features
    
    def analyze_lesion_connectivity(self, eeg_data, lesion_mask, electrode_positions=None):
        """Analyze connectivity patterns related to lesion locations"""
        
        # For demonstration, we'll create a simplified mapping between 
        # electrodes and brain regions affected by lesions
        
        # Calculate lesion center of mass
        if np.sum(lesion_mask) == 0:
            print("No lesion found in mask")
            return None
        
        lesion_coords = np.where(lesion_mask > 0)
        lesion_center = [np.mean(lesion_coords[i]) for i in range(3)]
        lesion_volume = np.sum(lesion_mask > 0)
        
        # Simplified mapping: classify electrodes as "near lesion" or "far from lesion"
        # In practice, you would use proper head modeling and source localization
        
        # For demonstration, randomly assign some electrodes as "lesion-affected"
        n_channels = len(self.eeg_channels)
        lesion_probability = min(0.5, lesion_volume / (256**3 * 0.1))  # Simplified
        
        np.random.seed(42)  # For reproducibility
        lesion_affected = np.random.random(n_channels) < lesion_probability
        
        # Compute connectivity matrices
        coherence_matrix = self.compute_coherence_matrix(eeg_data)
        phase_coupling = self.compute_phase_coupling(eeg_data)
        mi_matrix = self.compute_mutual_information_matrix(eeg_data)
        
        # Analyze connectivity patterns
        lesion_analysis = {
            'lesion_center': lesion_center,
            'lesion_volume': lesion_volume,
            'lesion_affected_channels': lesion_affected,
            'affected_channel_names': [self.eeg_channels[i] for i in range(n_channels) if lesion_affected[i]],
            'connectivity_matrices': {
                'coherence': coherence_matrix,
                'phase_coupling': phase_coupling,
                'mutual_information': mi_matrix
            }
        }
        
        # Compute connectivity statistics
        affected_indices = np.where(lesion_affected)[0]
        unaffected_indices = np.where(~lesion_affected)[0]
        
        if len(affected_indices) > 0 and len(unaffected_indices) > 0:
            # Within-lesion connectivity
            within_lesion_conn = coherence_matrix[np.ix_(affected_indices, affected_indices)]
            within_lesion_mean = np.mean(within_lesion_conn[np.triu_indices_from(within_lesion_conn, k=1)])
            
            # Between lesion and healthy connectivity
            between_conn = coherence_matrix[np.ix_(affected_indices, unaffected_indices)]
            between_mean = np.mean(between_conn)
            
            # Within healthy connectivity
            within_healthy_conn = coherence_matrix[np.ix_(unaffected_indices, unaffected_indices)]
            within_healthy_mean = np.mean(within_healthy_conn[np.triu_indices_from(within_healthy_conn, k=1)])
            
            lesion_analysis['connectivity_stats'] = {
                'within_lesion_connectivity': within_lesion_mean,
                'between_connectivity': between_mean,
                'within_healthy_connectivity': within_healthy_mean,
                'lesion_vs_healthy_ratio': within_lesion_mean / within_healthy_mean if within_healthy_mean > 0 else np.inf
            }
        
        return lesion_analysis
    
    def visualize_connectivity_results(self, subject_id, connectivity_results, save_path=None):
        """Create comprehensive visualization of connectivity results"""
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Plot coherence matrix
        im1 = axes[0, 0].imshow(connectivity_results['connectivity_matrices']['coherence'], 
                               cmap='viridis', vmin=0, vmax=1)
        axes[0, 0].set_title('Coherence Matrix')
        axes[0, 0].set_xlabel('Channels')
        axes[0, 0].set_ylabel('Channels')
        plt.colorbar(im1, ax=axes[0, 0])
        
        # Plot phase coupling matrix
        im2 = axes[0, 1].imshow(connectivity_results['connectivity_matrices']['phase_coupling'], 
                               cmap='plasma', vmin=0, vmax=1)
        axes[0, 1].set_title('Phase Coupling Matrix')
        axes[0, 1].set_xlabel('Channels')
        axes[0, 1].set_ylabel('Channels')
        plt.colorbar(im2, ax=axes[0, 1])
        
        # Plot mutual information matrix
        im3 = axes[0, 2].imshow(connectivity_results['connectivity_matrices']['mutual_information'], 
                               cmap='inferno')
        axes[0, 2].set_title('Mutual Information Matrix')
        axes[0, 2].set_xlabel('Channels')
        axes[0, 2].set_ylabel('Channels')
        plt.colorbar(im3, ax=axes[0, 2])
        
        # Plot connectivity statistics if available
        if 'connectivity_stats' in connectivity_results:
            stats = connectivity_results['connectivity_stats']
            categories = ['Within Lesion', 'Between', 'Within Healthy']
            values = [stats['within_lesion_connectivity'], 
                     stats['between_connectivity'],
                     stats['within_healthy_connectivity']]
            
            axes[1, 0].bar(categories, values, color=['red', 'orange', 'green'], alpha=0.7)
            axes[1, 0].set_title('Connectivity Statistics')
            axes[1, 0].set_ylabel('Mean Coherence')
            axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Plot lesion-affected channels
        affected_channels = connectivity_results.get('affected_channel_names', [])
        all_channels = self.eeg_channels
        
        channel_colors = ['red' if ch in affected_channels else 'blue' for ch in all_channels]
        axes[1, 1].bar(range(len(all_channels)), [1]*len(all_channels), color=channel_colors, alpha=0.7)
        axes[1, 1].set_xticks(range(len(all_channels)))
        axes[1, 1].set_xticklabels(all_channels, rotation=45)
        axes[1, 1].set_title('Lesion-Affected Channels (Red)')
        axes[1, 1].set_ylabel('Channel Status')
        
        # Summary text
        lesion_volume = connectivity_results.get('lesion_volume', 0)
        n_affected = len(affected_channels)
        
        summary_text = f"""Subject {subject_id} Summary:
        
Lesion Volume: {lesion_volume:,} voxels
Affected Channels: {n_affected}/{len(all_channels)}
Affected: {', '.join(affected_channels)}

Connectivity Metrics:
- Mean Coherence: {np.mean(connectivity_results['connectivity_matrices']['coherence']):.3f}
- Mean Phase Coupling: {np.mean(connectivity_results['connectivity_matrices']['phase_coupling']):.3f}
- Mean Mutual Info: {np.mean(connectivity_results['connectivity_matrices']['mutual_information']):.3f}
"""
        
        axes[1, 2].text(0.1, 0.5, summary_text, transform=axes[1, 2].transAxes, 
                        fontsize=10, verticalalignment='center', fontfamily='monospace')
        axes[1, 2].axis('off')
        
        plt.suptitle(f'Connectivity Analysis - Subject {subject_id}', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Connectivity visualization saved: {save_path}")
        
        plt.show()
        return fig

def main():
    """Demonstrate time series and connectivity analysis"""
    print("Time Series and Connectivity Analysis")
    print("="*50)
    
    # Initialize analyzer
    analyzer = TimeSeriesConnectivityAnalyzer()
    
    # Analyze a few subjects
    subjects_to_analyze = [1, 2, 3]
    
    for subject_id in subjects_to_analyze:
        print(f"\n--- ANALYZING SUBJECT {subject_id} ---")
        
        try:
            # Load EEG data
            eeg_data, _ = analyzer.load_eeg_data(subject_id)
            print(f"EEG data loaded: {eeg_data.shape}")
            
            # Load lesion mask
            lesion_mask, _ = analyzer.load_lesion_mask(subject_id)
            print(f"Lesion mask loaded: {lesion_mask.shape}")
            
            # Use subset of data for faster computation
            subset_data = eeg_data[:, :5000]  # First ~40 seconds
            
            # Analyze temporal dynamics
            temporal_features = analyzer.analyze_temporal_dynamics(subset_data)
            print(f"Temporal analysis: {len(temporal_features['time_points'])} time windows")
            
            # Analyze lesion connectivity
            lesion_connectivity = analyzer.analyze_lesion_connectivity(subset_data, lesion_mask)
            print(f"Lesion connectivity analysis completed")
            
            # Create visualization
            save_path = f"connectivity_analysis_subject_{subject_id}.png"
            analyzer.visualize_connectivity_results(subject_id, lesion_connectivity, save_path)
            
            # Store results
            analyzer.time_series_results[subject_id] = temporal_features
            analyzer.lesion_connectivity[subject_id] = lesion_connectivity
            
        except Exception as e:
            print(f"Error analyzing subject {subject_id}: {e}")
            continue
    
    print(f"\nTime series and connectivity analysis completed for {len(analyzer.lesion_connectivity)} subjects")
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
